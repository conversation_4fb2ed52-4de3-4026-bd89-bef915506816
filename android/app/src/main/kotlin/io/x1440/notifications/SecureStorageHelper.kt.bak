package io.x1440.notifications

import android.content.Context
import android.util.Log

/**
 * Helper class for securely storing and retrieving sensitive data.
 * Simplified implementation to avoid dependency issues with Kotlin 2.1.0
 */
class SecureStorageHelper {
    companion object {
        private const val TAG = "SecureStorageHelper"
        private const val KEYSTORE_PROVIDER = "AndroidKeyStore"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val IV_SEPARATOR = ":"
        private const val KEY_ALIAS = "x1440_secure_key"
        private const val PREFS_NAME = "X1440SecurePrefs"
        
        /**
         * Retrieve a securely stored string value.
         * @param context Application context
         * @param key The key to retrieve
         * @return The decrypted string value or null if not found or on error
         */
        fun getString(context: Context, key: String): String? {
            try {
                // Get encrypted data from SharedPreferences
                val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                val encryptedData = sharedPrefs.getString(key, null) ?: return null
                
                // Split IV and encrypted data
                val parts = encryptedData.split(IV_SEPARATOR)
                if (parts.size != 2) {
                    Log.e(TAG, "Invalid encrypted data format")
                    return null
                }
                
                val iv = Base64.decode(parts[0], Base64.DEFAULT)
                val encrypted = Base64.decode(parts[1], Base64.DEFAULT)
                
                // Get the key from the Android KeyStore
                val keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER)
                keyStore.load(null)
                
                // Create or get the key if it doesn't exist
                if (!keyStore.containsAlias(KEY_ALIAS)) {
                    Log.d(TAG, "Key doesn't exist, creating new one")
                    createSecretKey()
                    return null // Key was just created, so no data could have been encrypted with it yet
                }
                
                val secretKeyEntry = keyStore.getEntry(KEY_ALIAS, null) as? KeyStore.SecretKeyEntry
                    ?: return null
                val secretKey = secretKeyEntry.secretKey
                
                // Decrypt the data
                val cipher = Cipher.getInstance(TRANSFORMATION)
                cipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(128, iv))
                val decryptedBytes = cipher.doFinal(encrypted)
                
                return String(decryptedBytes, StandardCharsets.UTF_8)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error decrypting value", e)
                return null
            }
        }
        
        /**
         * Securely store a string value.
         * @param context Application context
         * @param key The key to store under
         * @param value The string value to encrypt and store
         * @return True if stored successfully, false otherwise
         */
        fun putString(context: Context, key: String, value: String): Boolean {
            try {
                // Get or create the key from the Android KeyStore
                val keyStore = KeyStore.getInstance(KEYSTORE_PROVIDER)
                keyStore.load(null)
                
                if (!keyStore.containsAlias(KEY_ALIAS)) {
                    createSecretKey()
                }
                
                val secretKeyEntry = keyStore.getEntry(KEY_ALIAS, null) as? KeyStore.SecretKeyEntry
                    ?: return false
                val secretKey = secretKeyEntry.secretKey
                
                // Encrypt the data
                val cipher = Cipher.getInstance(TRANSFORMATION)
                cipher.init(Cipher.ENCRYPT_MODE, secretKey)
                val iv = cipher.iv
                
                // Get the bytes to encrypt
                val valueBytes = value.toByteArray(StandardCharsets.UTF_8)
                val encryptedBytes = cipher.doFinal(valueBytes)
                
                // Encode the IV and encrypted data as Base64 and store them
                val ivString = Base64.encodeToString(iv, Base64.DEFAULT)
                val encryptedString = Base64.encodeToString(encryptedBytes, Base64.DEFAULT)
                val combined = "$ivString$IV_SEPARATOR$encryptedString"
                
                // Store in SharedPreferences
                val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                sharedPrefs.edit().putString(key, combined).apply()
                
                return true
                
            } catch (e: Exception) {
                Log.e(TAG, "Error encrypting value", e)
                return false
            }
        }
        
        /**
         * Remove a securely stored value.
         * @param context Application context
         * @param key The key to remove
         */
        fun remove(context: Context, key: String) {
            try {
                val sharedPrefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
                sharedPrefs.edit().remove(key).apply()
            } catch (e: Exception) {
                Log.e(TAG, "Error removing key", e)
            }
        }
        
        /**
         * Create a new secret key in the Android KeyStore.
         */
        private fun createSecretKey() {
            try {
                val keyGenerator = KeyGenerator.getInstance(
                    KeyProperties.KEY_ALGORITHM_AES, 
                    KEYSTORE_PROVIDER
                )
                
                val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                    KEY_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                )
                    .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                    .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                    .setKeySize(256)
                    .build()
                
                keyGenerator.init(keyGenParameterSpec)
                keyGenerator.generateKey()
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating secret key", e)
            }
        }
    }
}
