package io.x1440.notifications

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import org.json.JSONException
import org.json.JSONObject

/**
 * Helper class for parsing notification payloads and extracting attachments.
 */
object PayloadParser {
    private const val TAG = "PayloadParser"

    /**
     * Clean the payload string to ensure it's valid JSON.
     */
    fun cleanPayload(payload: String): String {
        return payload.replace("\\\"", "\"")
            .replace("\"{", "{")
            .replace("}\"", "}")
    }

    /**
     * Parse JSON string to map.
     */
    fun parseJsonToMap(jsonString: String): Map<String, Any> {
        return try {
            Gson().fromJson(jsonString, object : TypeToken<Map<String, Any>>() {}.type)
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing JSON: $jsonString", e)
            emptyMap()
        }
    }

    /**
     * Extract attachments from a notification payload.
     */
    fun processAttachments(context: Context, payload: String?): List<String> {
        if (payload.isNullOrEmpty()) return emptyList()
        
        try {
            val payloadStr = cleanPayload(payload)
            Log.d(TAG, "Processing payload: $payloadStr")

            val dictionary: Map<String, Any> = parseJsonToMap(payloadStr)
            
            // Try to extract attachments from x1440Payload
            if (dictionary["x1440Payload"] is Map<*, *>) {
                @Suppress("UNCHECKED_CAST")
                val x1440Payload = dictionary["x1440Payload"] as? Map<String, Any>
                if (x1440Payload != null) {
                    return scanPayloadForAttachments(context, x1440Payload)
                }
            } else {
                // Try with a token payload structure
                val tokenKey = "x1440_token"
                if (dictionary.containsKey(tokenKey)) {
                    val tokenValue = dictionary[tokenKey] as? String
                    if (!tokenValue.isNullOrEmpty()) {
                        // This is a short-form payload with a token reference
                        Log.d(TAG, "Found token reference, token: $tokenValue")
                        return emptyList() // Token handling would be done in WorkManager
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing attachments", e)
        }
        return emptyList()
    }

    /**
     * Scan the x1440Payload for attachments.
     */
    fun scanPayloadForAttachments(context: Context, x1440Payload: Map<String, Any>): List<String> {
        val attachmentUrls = mutableListOf<String>()
        val instanceUrl = SecureStorageHelper.getString(context, "instance_url") ?: return emptyList()

        try {
            // Extract message
            val message = x1440Payload["message"] as? Map<*, *> ?: return emptyList()
            
            // Extract conversation entry
            val conversationEntry = message["conversationEntry"] as? Map<*, *> ?: return emptyList()
            
            // Extract entry payload
            val entryPayload = conversationEntry["entryPayload"] as? Map<*, *> ?: return emptyList()
            
            // Extract message text metadata
            val messageText = entryPayload["messageText"] as? Map<*, *> ?: return emptyList()
            
            // Extract message attachments
            val attachments = messageText["attachments"] as? List<*> ?: return emptyList()
            
            // Process each attachment
            for (attachment in attachments) {
                if (attachment is Map<*, *>) {
                    val attachmentRecord = attachment["attachmentRecord"] as? Map<*, *> ?: continue
                    val identifier = attachmentRecord["id"] as? String ?: continue
                    
                    // Process standard attachment types
                    val urls = extractAttachmentUrls(attachmentRecord, instanceUrl, identifier)
                    if (urls.isNotEmpty()) {
                        attachmentUrls.addAll(urls)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error scanning payload for attachments", e)
        }

        return attachmentUrls
    }
    
    /**
     * Extract URLs from an attachment record.
     */
    private fun extractAttachmentUrls(
        attachmentRecord: Map<*, *>, 
        instanceUrl: String, 
        identifier: String
    ): List<String> {
        val urls = mutableListOf<String>()
        
        // Get content document link
        val contentDocumentLink = attachmentRecord["contentDocument"] as? Map<*, *>
        
        if (contentDocumentLink != null) {
            val contentDocument = contentDocumentLink["contentDocument"] as? Map<*, *>
            
            if (contentDocument != null) {
                val latestPublishedVersionId = contentDocument["latestPublishedVersionId"] as? String
                
                if (!latestPublishedVersionId.isNullOrEmpty()) {
                    // Format content URL based on instance URL and version ID
                    val finalUrl = "$instanceUrl/sfc/servlet.shepherd/version/download/$latestPublishedVersionId"
                    urls.add(finalUrl)
                }
            }
        }
        
        return urls
    }
}
