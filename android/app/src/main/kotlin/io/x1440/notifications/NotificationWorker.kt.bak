package io.x1440.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.work.Worker
import androidx.work.WorkerParameters
import io.x1440.MainActivity
import io.x1440.R
import org.json.JSONObject

/**
 * Worker class that processes FCM messages in the background.
 * This prevents ANR issues by performing all heavy notification processing
 * off the main thread with WorkManager's guarantees.
 */
class NotificationWorker(
    context: Context,
    params: WorkerParameters
) : Worker(context, params) {
    private val appContext: Context = context

    override fun doWork(): Result {
        Log.d(TAG, "Starting notification processing work")
        
        try {
            // Get the FCM data from input
            val dataJson = inputData.getString(KEY_FCM_DATA)
            if (dataJson.isNullOrEmpty()) {
                Log.e(TAG, "No FCM data provided to worker")
                return Result.failure()
            }
            
            // Parse the notification data
            parseAndCreateNotification(dataJson)
            
            Log.d(TAG, "Notification processing completed successfully")
            return Result.success()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in notification worker", e)
            return Result.failure()
        }
    }
    
    private fun parseAndCreateNotification(dataJson: String) {
        try {
            val json = JSONObject(dataJson)
            
            // Extract notification data with fallbacks
            val title = json.optString("title", "X1440 Notification")
            val body = json.optString("body", "You have a new notification")
            val channelId = json.optString("channel_id", CHANNEL_ID)
            
            // Create notification
            createNotification(title, body, channelId)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing notification data", e)
            // Create a fallback notification with generic content
            createNotification("X1440 Notification", "You have a new notification", CHANNEL_ID)
        }
    }

    private fun createNotification(title: String, body: String, channelId: String) {
        try {
            // Create an intent to open the app when notification is clicked
            val intent = Intent(appContext, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            }
            
            // Set up the pending intent with proper flags based on Android version
            val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
            
            val pendingIntent = PendingIntent.getActivity(
                appContext,
                0,
                intent,
                pendingIntentFlags
            )
            
            // Get the notification manager service
            val notificationManager = 
                appContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // For Android Oreo and above, create a notification channel
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    channelId,
                    CHANNEL_NAME,
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    enableLights(true)
                    lightColor = Color.BLUE
                    enableVibration(true)
                }
                notificationManager.createNotificationChannel(channel)
            }
            
            // Build the notification
            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            
            // Using apply for better readability and to avoid chaining
            val notificationBuilder = NotificationCompat.Builder(appContext, channelId).apply {
                setSmallIcon(R.drawable.ic_notification)  // Using the drawable we created earlier
                setContentTitle(title)
                setContentText(body)
                setAutoCancel(true)  // Notification disappears when tapped
                setSound(defaultSoundUri)
                setContentIntent(pendingIntent)
                priority = NotificationCompat.PRIORITY_HIGH
            }
            
            // Show the notification with a unique ID
            notificationManager.notify(System.currentTimeMillis().toInt(), notificationBuilder.build())
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating notification", e)
        }
    }

    companion object {
        private const val TAG = "NotificationWorker"
        private const val KEY_FCM_DATA = "key_fcm_data"
        private const val CHANNEL_ID = "x1440_default_channel"
        private const val CHANNEL_NAME = "X1440 Notifications"
    }
}
