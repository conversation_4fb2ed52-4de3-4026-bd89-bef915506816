import 'dart:async';
import 'dart:convert';
import 'dart:isolate';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:uuid/uuid.dart';
import 'package:x1440/api/isolate/network_isolate_types.dart';
import 'package:x1440/use_cases/auth/auth_use_case.dart';
import 'package:x1440/utils/json_utils.dart' as json_utils;

/// Endpoint prefix for logging - used in simplified interceptors
const String loggingEndPointPrefix = '/logging';

/// Class to encapsulate network isolate request parameters adapted for the new isolate implementation
/// This bridges the new implementation with the existing types
class NetworkIsolateRequest {
  final ApiType apiType;
  final String method;
  final String path;
  final String? baseUrl; // Added baseUrl parameter
  final Map<String, dynamic>? queryParams;
  final dynamic data;
  final String? accessToken;
  final String? authorizationToken;
  final Map<String, String>? headers; // Added headers support

  NetworkIsolateRequest({
    required this.apiType,
    required this.method,
    required this.path,
    this.baseUrl, // Optional but recommended
    this.queryParams,
    this.data,
    this.accessToken,
    this.authorizationToken,
    this.headers, // Optional headers
  });
  
  // Convert to NetworkRequest for compatibility with existing types
  NetworkRequest toNetworkRequest() {
    final id = const Uuid().v4();
    return NetworkRequest(
      id: id,
      apiType: apiType,
      method: method,
      parameters: {
        'path': path,
        'baseUrl': baseUrl, // Pass baseUrl in parameters
        'queryParams': queryParams ?? {},
        'data': data,
      },
      headers: headers, // Forward headers to the network request
      authToken: authorizationToken, // For Shim Service API
      sessionToken: accessToken,     // For Salesforce API
    );
  }
}

/// Helper function for logging from background isolate
void _logIsolate(String message) {
  print('🧵 BACKGROUND ISOLATE - $message');
}

/// Helper function to mask tokens for logging
String _maskToken(String? token) {
  if (token == null || token.isEmpty) return 'null';  
  if (token.length <= 8) return '****${token.substring(token.length - 4)}';  
  return '${token.substring(0, 4)}****${token.substring(token.length - 4)}'; 
}

/// Main class for managing the network isolate
class NetworkIsolate {
  // Isolate references for communication
  Isolate? _isolate;
  ReceivePort? _receivePort;
  SendPort? _sendPort;
  
  // Request tracking
  final Map<String, Completer<dynamic>> _pendingRequests = {};
  
  // Configuration
  final bool _enableLogging;
  final Duration _requestTimeout;
  final Map<ApiType, String> _baseUrls;
  
  // Authentication tokens
  String? _accessToken;
  String? _authorizationToken;

  // Factory singleton pattern
  static NetworkIsolate? _instance;
  
  // Fields will be initialized during constructor or initialize() method
  
  // Private constructor
  NetworkIsolate._internal({
    required bool enableLogging,
    required Duration requestTimeout,
    required Map<ApiType, String> baseUrls,
  }) : _enableLogging = enableLogging,
       _requestTimeout = requestTimeout,
       _baseUrls = baseUrls;
       
  /// Gets the singleton instance
  static NetworkIsolate getInstance({
    Duration? requestTimeout = const Duration(seconds: 30),
    bool? enableLogging = false,
    Map<ApiType, String>? baseUrls,
  }) {
    _instance ??= NetworkIsolate._internal(
      requestTimeout: requestTimeout!,
      enableLogging: enableLogging!,
      baseUrls: baseUrls ?? {},
    );
    return _instance!;
  }
  
  // Removed nested classes that are no longer needed with new implementation
  
  /// Non-blocking function to process request params off the main thread
  /// Helper function to process request parameters in a background isolate
  static NetworkRequest _processRequestOffMainThread(Map<String, dynamic> params) {
    final request = params['request'] as NetworkIsolateRequest;
    final baseUrls = params['baseUrls'] as Map<ApiType, String>;
    final enableLogging = params['enableLogging'] as bool;
    
    // Apply base URL if it exists for this API type
    NetworkIsolateRequest modifiedRequest = request;
    if (request.baseUrl == null && baseUrls.containsKey(request.apiType)) {
      modifiedRequest = NetworkIsolateRequest(
        apiType: request.apiType,
        method: request.method,
        path: request.path,
        baseUrl: baseUrls[request.apiType],
        queryParams: request.queryParams,
        data: request.data,
        accessToken: request.accessToken,
        authorizationToken: request.authorizationToken,
      );
      
      if (enableLogging) {
        print('🔁 BACKGROUND - Applied baseUrl for ${request.apiType.name}: ${baseUrls[request.apiType]}');
      }
    }

    // Convert to NetworkRequest
    final networkRequest = modifiedRequest.toNetworkRequest();
    final requestId = networkRequest.id;
    
    // Note: Timeout determination moved to _getTimeoutForEndpoint method
    // which is used by the main sendRequest method instead
    
    if (enableLogging) {
      final apiTypeStr = request.apiType.toString().split('.').last;
      final methodStr = request.method.toUpperCase();
      print('✅ BACKGROUND - Processed $apiTypeStr $methodStr request to ${request.path} [$requestId]');
    }
    
    // Return the network request directly
    return networkRequest;
  }

  /// Send a network request through the isolate with non-blocking implementation
  Future<dynamic> sendRequest(NetworkIsolateRequest request) async {
    if (_sendPort == null) {
      throw Exception('Network isolate not initialized');
    }
    
    final stopwatch = Stopwatch()..start();
    
    try {
      // Process request parameters off the main thread using compute
      // This is where we prevent the blocking by moving work to a separate isolate
      final networkRequest = await compute(
        _processRequestOffMainThread,
        {
          'request': request,
          'baseUrls': Map<ApiType, String>.from(_baseUrls), // Copy to avoid concurrent modification
          'enableLogging': _enableLogging,
        },
      );
      
      final requestId = networkRequest.id;
      
      // Create completer to track the response
      final completer = Completer<dynamic>();
      _pendingRequests[requestId] = completer;
      
      // Update base URLs if needed - this is a lightweight operation
      _updateBaseUrl(request);
      
      if (_enableLogging) {
        final apiTypeStr = request.apiType.toString().split('.').last;
        final methodStr = request.method.toUpperCase();
        print('📤 MAIN ISOLATE - Sending $apiTypeStr $methodStr request to ${request.path} [$requestId]');
      }
      
      // Set up lightweight progress logging timer
      Timer? progressTimer;
      if (_enableLogging) {
        progressTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
          if (!completer.isCompleted && stopwatch.elapsedMilliseconds > 1000) {
            print('⏳ MAIN ISOLATE - Waiting for $requestId (${stopwatch.elapsedMilliseconds}ms) - ${request.path}');
          } else {
            timer.cancel();
          }
        });
      }
      
      // Determine appropriate timeout based on endpoint
      final Duration requestTimeout = _getTimeoutForEndpoint(request.path);
      
      // Set up timeout with non-blocking behavior
      Timer(
        requestTimeout,
        () {
          if (_pendingRequests.containsKey(requestId)) {
            if (_enableLogging) {
              print('⏱️ MAIN ISOLATE - Request $requestId timed out after ${requestTimeout.inSeconds}s');
            }
            _pendingRequests.remove(requestId)?.completeError(
              DioException(
                requestOptions: RequestOptions(path: request.path),
                error: 'Request timeout after ${requestTimeout.inSeconds} seconds',
                type: DioExceptionType.connectionTimeout,
              ),
            );
          }
        }
      );
      
      // Send request to isolate - this operation just sends a message and returns immediately
      // so it doesn't block the main thread
      _sendPort!.send(networkRequest.toJson());
      
      if (_enableLogging) {
        print('📤 MAIN ISOLATE - Request sent to isolate [$requestId]');
      }
      
      // Return the completer's future - this allows the caller to await the response
      // without blocking the main thread
      return completer.future.then(
        (response) {
          final elapsed = stopwatch.elapsed.inMilliseconds;
          progressTimer?.cancel();
          
          if (_enableLogging) {
            print('📥 MAIN ISOLATE - Received response for $requestId in ${elapsed}ms');
          }
          
          return response;
        },
        onError: (e) {
          final elapsed = stopwatch.elapsed.inMilliseconds;
          progressTimer?.cancel();
          
          if (_enableLogging) {
            print('❌ MAIN ISOLATE - Error for $requestId in ${elapsed}ms: $e');
          }
          
          throw e;
        },
      );
    } catch (e) {
      if (_enableLogging) {
        print('💥 MAIN ISOLATE - Failed to process request: $e');
      }
      rethrow;
    }
  }
  
  /// Helper method to update base URL for an API type
  void _updateBaseUrl(NetworkIsolateRequest request) {
    if (request.baseUrl != null && request.baseUrl!.isNotEmpty) {
      _baseUrls[request.apiType] = request.baseUrl!;
    }
  }
  
  // The _sendRequestThroughIsolate helper method was removed as part of the refactoring
  // to eliminate blocking behavior. Its functionality is now integrated directly into
  // the sendRequest method using compute() for non-blocking behavior.
  
  /// Determines the appropriate timeout duration based on endpoint
  Duration _getTimeoutForEndpoint(String path) {
    // Use shorter timeout for logging endpoints
    if (path.contains(loggingEndPointPrefix)) {
      return const Duration(seconds: 5);
    }
    
    // Use longer timeout for authentication operations (shim service can be slow)
    if (path.contains('/auth/') || path.contains('/salesforce/refresh-token')) {
      return const Duration(seconds: 30);
    } 
    
    // Use moderate timeout for regular API calls
    if (path.contains('/api/v') || path.contains('/services/data/')) {
      return const Duration(seconds: 20);
    }
    
    // Use longer timeout for potentially slow operations
    if (path.contains('/upload/') || 
        path.contains('/download/') || 
        path.contains('/process/')) {
      return const Duration(minutes: 2);
    }
    
    // Default to the configured request timeout
    return _requestTimeout;
  }

  /// Initialize the network isolate
  Future<void> initialize() async {
    // Check if already initialized
    if (_isolate != null) {
      if (_enableLogging) {
        print('🔄 MAIN ISOLATE - Network isolate already running');
      }
      return;
    }

    if (_enableLogging) {
      print('🚀 MAIN ISOLATE - Starting network isolate');
    }

    // Create completer to track initialization state
    final completer = Completer<void>();
    
    // Create separate ports for handshake and ongoing communication
    final handshakePort = ReceivePort();
    
    try {
      if (_enableLogging) {
        print('🔧 MAIN ISOLATE - Creating handshake ReceivePort: $handshakePort');
        print('🔧 MAIN ISOLATE - About to spawn isolate with handshake SendPort: ${handshakePort.sendPort}');
      }
      
      // Spawn isolate without waiting directly (avoid blocking main thread)
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        handshakePort.sendPort,
      );
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Isolate spawned successfully: $_isolate');
        print('⏳ MAIN ISOLATE - Waiting for handshake response from isolate...');
      }
      
      // Use timeout to prevent indefinite waiting
      _sendPort = await handshakePort.first.timeout(
        const Duration(seconds: 2),
        onTimeout: () {
          throw TimeoutException('Network isolate handshake timed out');
        },
      ) as SendPort;
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Received handshake SendPort from isolate: $_sendPort');
        print('🔧 MAIN ISOLATE - Creating main communication ReceivePort...');
      }
      
      // Now create the main communication port
      _receivePort = ReceivePort();
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Created main communication ReceivePort: $_receivePort');
        print('🔧 MAIN ISOLATE - Sending main communication SendPort to isolate: ${_receivePort!.sendPort}');
      }
      
      // Send our main communication port to the isolate
      _sendPort!.send(_receivePort!.sendPort);
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Sent main communication SendPort to isolate');
        print('🔧 MAIN ISOLATE - Setting up response listener...');
      }
      
      // Set up listener for responses
      _receivePort!.listen(_handleIsolateResponse);
      
      // Close the handshake port as it's no longer needed
      handshakePort.close();
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Network isolate initialized successfully');
        print('🔧 MAIN ISOLATE - Final SendPort for requests: $_sendPort');
      }
      
      completer.complete();
    } catch (e) {
      // Clean up resources on error
      handshakePort.close();
      _isolate?.kill(priority: Isolate.immediate);
      _isolate = null;
      
      if (_enableLogging) {
        print('❌ MAIN ISOLATE - Failed to initialize network isolate: $e');
      }
      
      completer.completeError(e);
    }
    
    // Return the future but don't await it directly
    // This allows the main thread to continue while isolate setup completes
    return completer.future;
  }

  /// Dispose the network isolate when it's no longer needed
  void dispose() {
    if (_isolate != null) {
      if (_enableLogging) {
        print('🛑 MAIN ISOLATE - Stopping network isolate');
      }
      _isolate!.kill(priority: Isolate.immediate);
      _isolate = null;
    }

    if (_receivePort != null) {
      _receivePort!.close();
      _receivePort = null;
    }

    _sendPort = null;
    _pendingRequests.clear();
  }

  /// Entry point for the background isolate
  static void _isolateEntryPoint(SendPort handshakeSendPort) {
    print('🔍 DIAGNOSTIC - ISOLATE ENTRY POINT CALLED');
    print('🔧 BACKGROUND ISOLATE - Received handshake SendPort: $handshakeSendPort');
    
    // Create a receive port for the initial handshake
    final handshakePort = ReceivePort();
    print('🔧 BACKGROUND ISOLATE - Created handshake ReceivePort: $handshakePort');
    
    // Declare the main send port at a wider scope so it's available to all callbacks
    late final SendPort mainSendPort;
    
    // Send the port to the main isolate for initial handshake
    print('🔧 BACKGROUND ISOLATE - Sending handshake SendPort to main isolate: ${handshakePort.sendPort}');
    handshakeSendPort.send(handshakePort.sendPort);
    print('✅ BACKGROUND ISOLATE - Handshake SendPort sent to main isolate');
    
    // Wait for the main isolate to send its main communication port
    print('⏳ BACKGROUND ISOLATE - Waiting for main communication port from main isolate...');
    handshakePort.first.then((mainReceivePort) {
      print('✅ BACKGROUND ISOLATE - Received main communication port: $mainReceivePort');
      
      // Close the handshake port as it's no longer needed
      handshakePort.close();
      print('🔧 BACKGROUND ISOLATE - Closed handshake port');
      
      // Now create a new receive port for ongoing communication
      final receivePort = ReceivePort();
      print('🔧 BACKGROUND ISOLATE - Created main communication ReceivePort: $receivePort');
      
      // Store the main isolate's send port for later use
      mainSendPort = mainReceivePort as SendPort;
      print('🔧 BACKGROUND ISOLATE - Stored main isolate SendPort: $mainSendPort');
      
      // Send this port to the main isolate
      print('🔧 BACKGROUND ISOLATE - Sending main communication SendPort to main isolate: ${receivePort.sendPort}');
      mainSendPort.send(receivePort.sendPort);
      print('✅ BACKGROUND ISOLATE - Main communication SendPort sent to main isolate');
      
      // Keep track of tokens
      String? accessToken;
      String? authorizationToken;
      
      // Create Dio instances for each API type
      final dioInstances = <ApiType, Dio>{};
      
      // Initialize Dio instances with proper logging
      final shimDio = Dio();
      final salesforceDio = Dio();
      
      // Print info logs for each request in the isolate
      _logIsolate('Setting up PrettyDioLogger for Dio instances');
      
      // SUPER EXPLICIT DEBUG PRINTS - THESE SHOULD ALWAYS SHOW UP
      debugPrint('🚨🚨🚨 ISOLATE DEBUG CHECK 1 - PRINTS VISIBLE?');
      print('🚨🚨🚨 ISOLATE DEBUG CHECK 2 - REGULAR PRINT VISIBLE?');
      
      // Add streamlined PrettyDioLogger to shim service Dio instance
      const logEnabled = true; // Always enable cleaner logs
      
      if (logEnabled) {
        final shimPrettyLogger = PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 120,
          logPrint: (obj) {
            // Clean logging without diagnostic noise
            print('🔵 [ISOLATE:ShimService] $obj');
          }
        );
        
        // Add simplified request/response logging interceptor
        shimDio.interceptors.add(InterceptorsWrapper(
          onRequest: (options, handler) {
            print('🔷 [ISOLATE:ShimService] Sending ${options.method} request to ${options.uri}');
            return handler.next(options);
          },
          onResponse: (response, handler) {
            print('✅ [ISOLATE:ShimService] Received ${response.statusCode} response from ${response.requestOptions.uri}');
            return handler.next(response);
          },
          onError: (error, handler) {
            print('❌ [ISOLATE:ShimService] Error ${error.response?.statusCode ?? 'unknown'} from ${error.requestOptions.uri}: ${error.message}');
            return handler.next(error);
          },
        ));
        
        // Add the pretty logger
        shimDio.interceptors.add(shimPrettyLogger);
      }
      
      // Add streamlined PrettyDioLogger to salesforce Dio instance with consistent format
      if (logEnabled) {
        // Configure Salesforce logger with same format as ShimService
        final salesforcePrettyLogger = PrettyDioLogger(
          requestHeader: true,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 120,
          logPrint: (obj) {
            // Clean logging without diagnostic noise, consistent with ShimService
            print('🔴 [ISOLATE:Salesforce] $obj');
          }
        );
        
        // Add simplified request/response logging interceptor for Salesforce
        salesforceDio.interceptors.add(InterceptorsWrapper(
          onRequest: (options, handler) {
            print('🔶 [ISOLATE:Salesforce] Sending ${options.method} request to ${options.uri}');
            return handler.next(options);
          },
          onResponse: (response, handler) {
            print('✅ [ISOLATE:Salesforce] Received ${response.statusCode} response from ${response.requestOptions.uri}');
            return handler.next(response);
          },
          onError: (error, handler) {
            print('❌ [ISOLATE:Salesforce] Error ${error.response?.statusCode ?? 'unknown'} from ${error.requestOptions.uri}: ${error.message}');
            return handler.next(error);
          },
        ));
        
        // Add the pretty logger to Salesforce Dio
        salesforceDio.interceptors.add(salesforcePrettyLogger);
      }
      
      // Store the instances
      dioInstances[ApiType.shimService] = shimDio;
      dioInstances[ApiType.salesforce] = salesforceDio;

      // Store base URLs for each API type
      final baseUrls = <ApiType, String>{};

      _logIsolate('Background isolate started and ready for requests');

      // Listen for messages from the main isolate
      receivePort.listen((dynamic message) {
        print('🚨🚨🚨 ISOLATE REQUEST - Received message of type: ${message.runtimeType}');
        
        if (message is String) {
          print('🚨🚨🚨 ISOLATE REQUEST - Message is String, length: ${message.length}');
          print('🚨🚨🚨 ISOLATE REQUEST - First 200 chars: ${message.substring(0, message.length > 200 ? 200 : message.length)}...');
          
          // First check if this is a special token update message
          try {
            final Map<String, dynamic> jsonMessage = jsonDecode(message);
            if (jsonMessage['type'] == 'update_tokens') {
              // Handle token update message
              _logIsolate('🔑 ISOLATE - Received token update message');
              accessToken = jsonMessage['accessToken'] as String?;
              authorizationToken = jsonMessage['authorizationToken'] as String?;
              
              _logIsolate('🔐 ISOLATE - Updated tokens:');
              _logIsolate('🔐 ISOLATE - Authorization Token: ${_maskToken(authorizationToken)} (for Shim Service)');
              _logIsolate('🔐 ISOLATE - Access Token: ${_maskToken(accessToken)} (for Salesforce)');
              
              // Log warnings if tokens are null or empty
              if (authorizationToken == null || authorizationToken!.isEmpty) {
                _logIsolate('⚠️ ISOLATE - WARNING: authorizationToken is null or empty!');
              }
              if (accessToken == null || accessToken!.isEmpty) {
                _logIsolate('⚠️ ISOLATE - WARNING: accessToken is null or empty!');
              }
              
              // No need to send a response for this message type
              return;
            }
          } catch (jsonError) {
            _logIsolate('⚠️ ISOLATE - Error parsing message as JSON: $jsonError');
            // Continue to try parsing as NetworkRequest
          }
          
          try {
            // Try to parse the message as a network request
            final NetworkRequest request = NetworkRequest.fromJson(message);

            // Print diagnostic info to trace request flow
            print('🔍 DIAGNOSTIC - ISOLATE received request: ${request.method} for API type ${request.apiType.name}');
            print('🚨🚨🚨 ISOLATE REQUEST - Successfully parsed NetworkRequest with ID: ${request.id}');
            
            // Handle auth update message
            if (request.method == 'auth_update') {
              // Store both token types separately
              authorizationToken = request.authToken;
              accessToken = request.sessionToken;

              _logIsolate('🔐 AUTH_UPDATE - Updated tokens:');
              _logIsolate('🔐 AUTH_UPDATE - authorizationToken: ${_maskToken(authorizationToken)} (for Shim Service)');
              _logIsolate('🔐 AUTH_UPDATE - accessToken: ${_maskToken(accessToken)} (for Salesforce)');
              
              // DIAGNOSTIC - Are these actually set correctly?
              if (authorizationToken == null || authorizationToken!.isEmpty) {
                _logIsolate('⚠️ AUTH_UPDATE - WARNING: authorizationToken is null or empty!');
              }
              if (accessToken == null || accessToken!.isEmpty) {
                _logIsolate('⚠️ AUTH_UPDATE - WARNING: accessToken is null or empty!');
              }

              // Send acknowledgment
              final response = NetworkResponse(
                id: request.id,
                success: true,
                data: {'status': 'tokens_updated'},
                statusCode: 200,
              );

              mainSendPort.send(response.toJson());
              return;
            }

            // Handle base URL update message
            if (request.method == 'base_url_update') {
              final Map<String, dynamic> params = request.parameters;
              final Map<String, dynamic> baseUrlsParam = params['baseUrls'] as Map<String, dynamic>;

              baseUrlsParam.forEach((key, value) {
                final apiType = ApiType.values.firstWhere((element) => element.name == key);
                baseUrls[apiType] = value as String;
              });

              _logIsolate('Updated base URLs: $baseUrls');

              // Send acknowledgment
              final response = NetworkResponse(
                id: request.id,
                success: true,
                data: {'status': 'base_urls_updated'},
                statusCode: 200,
              );

              mainSendPort.send(response.toJson());
              return;
            }

            // Get the right Dio instance
            final dio = dioInstances[request.apiType];
            if (dio == null) {
              throw Exception('Unknown API type: ${request.apiType}');
            }
            
            // SUPER DIAGNOSTIC - Which Dio instance are we using?
            print('🧩🧩🧩 DIAGNOSTIC - Using Dio instance for ${request.apiType}');
            print('🧩🧩🧩 DIAGNOSTIC - Dio instance interceptors count: ${dio.interceptors.length}');
            for (int i = 0; i < dio.interceptors.length; i++) {
              print('🧩🧩🧩 DIAGNOSTIC - Interceptor $i type: ${dio.interceptors[i].runtimeType}');
            }
            if (dio == shimDio) {
              print('✅✅✅ DIAGNOSTIC - Confirmed using shimDio instance');
            } else if (dio == salesforceDio) {
              print('✅✅✅ DIAGNOSTIC - Confirmed using salesforceDio instance');
            } else {
              print('❌❌❌ DIAGNOSTIC - Using UNKNOWN Dio instance');
            }

            // Extract path and other parameters
            final Map<String, dynamic> params = request.parameters;
            final String path = params['path'] as String;
            final String? baseUrl = params['baseUrl'] as String?;
            final Map<String, dynamic>? queryParams = params['queryParams'] as Map<String, dynamic>?;
            final dynamic data = params['data'];

            // If baseUrl is provided in the request, update it in the Dio instance temporarily
            String? originalBaseUrl;
            if (baseUrl != null && baseUrl.isNotEmpty) {
              originalBaseUrl = dio.options.baseUrl;
              dio.options.baseUrl = baseUrl;
              _logIsolate('🌐 USING REQUEST-SPECIFIC BASE URL for ${request.apiType.name}: "$baseUrl" (original was: "$originalBaseUrl")');
            } else if (baseUrls.containsKey(request.apiType)) {
              // Use stored base URL if available
              originalBaseUrl = dio.options.baseUrl;
              dio.options.baseUrl = baseUrls[request.apiType]!;
              _logIsolate('🌐 USING STORED BASE URL for ${request.apiType.name}: "${baseUrls[request.apiType]}" (original was: "$originalBaseUrl")');
            }

            // Add auth headers based on API type
            final headers = <String, String>{
              'Content-Type': 'application/json',
            };
            if (request.headers != null && request.headers is Map<String, dynamic>) {
              final reqHeaders = request.headers as Map<String, dynamic>;
              reqHeaders.forEach((key, value) {
                headers[key] = value.toString();
              });
            }

            // ENHANCED DIAGNOSTIC: Log the current auth state
            print('🧵 BACKGROUND ISOLATE - 🔐 AUTH STATE CHECK - ' + 
                  'authorizationToken present: ${authorizationToken != null && authorizationToken!.isNotEmpty}, ' +
                  'accessToken present: ${accessToken != null && accessToken!.isNotEmpty}');
            
            // ENHANCED: Check if session endpoint is being called
            final isSessionEndpoint = request.parameters['path'].toString().contains('/organizations/') && 
                                     request.parameters['path'].toString().contains('/sessions');
            
            if (isSessionEndpoint) {
              print('🧵 BACKGROUND ISOLATE - 🚨 SESSION ENDPOINT DETECTED - Special handling for authorization');
              // SESSION ENDPOINTS MUST HAVE AUTH HEADER
            }

            // Add the correct authorization token based on API type
            // Only add authorization token for non-token-exchange endpoints
            if (request.apiType == ApiType.shimService) {
              final isTokenExchangeEndpoint = request.parameters['path'].toString().contains('/auth/salesforce/obtain-token');

              if (isTokenExchangeEndpoint) {
                print('🧵 BACKGROUND ISOLATE - ℹ️ TOKEN EXCHANGE ENDPOINT - No Authorization header needed');
              } else {
                // CRITICAL FIX: For all non-token-exchange shim service endpoints, always add Authorization header
                if (authorizationToken != null && authorizationToken!.isNotEmpty) {
                  print('🧵 BACKGROUND ISOLATE - ✅ AUTHORIZATION - Added proper Bearer token for shim service: ${_maskToken(authorizationToken!)}');
                  headers['Authorization'] = 'Bearer $authorizationToken';
                  
                  // EXTRA DIAGNOSTIC: Verify header was added
                  print('🧵 BACKGROUND ISOLATE - ✓ VERIFICATION - Authorization header set: ${headers.containsKey("Authorization")}');
                  
                  // CRITICAL FIX: Add X-1440-Session-Token header for shim service requests
                  // In this implementation, accessToken variable actually contains the session token
                  if (accessToken != null && accessToken!.isNotEmpty) {
                    headers['X-1440-Session-Token'] = accessToken!; // Non-null assertion since we checked it's not null
                    print('🧵 BACKGROUND ISOLATE - ✅ SESSION TOKEN - Added X-1440-Session-Token header: ${_maskToken(accessToken)}');
                  } else {
                    print('🧵 BACKGROUND ISOLATE - ⚠️ SESSION TOKEN - Missing session token for shim service request!');
                  }
                } else {
                  print('🧵 BACKGROUND ISOLATE - ⚠️ AUTHORIZATION - No authorizationToken available for shim service request!');
                  print('🧵 BACKGROUND ISOLATE - 🔍 DEBUG INFO - Path: ${request.parameters['path']}, Method: ${request.method}');
                }
              }
            } else if (request.apiType == ApiType.salesforce) {
              if (accessToken != null && accessToken!.isNotEmpty) {
                print('🧵 BACKGROUND ISOLATE - ✅ AUTHORIZATION - Added proper Bearer token for salesforce: ${_maskToken(accessToken!)}');
                headers['Authorization'] = 'Bearer $accessToken';
                _logIsolate('✅ AUTHORIZATION - Added proper Bearer token for Salesforce: ${_maskToken(accessToken)}');
              } else {
                _logIsolate('⚠️ AUTHORIZATION - No accessToken available for Salesforce API');
              }
            } else {
              _logIsolate('⚠️ AUTHORIZATION - Unknown API type ${request.apiType.name}: authToken=${authorizationToken != null ? 'SET' : 'NULL'}, accessToken=${accessToken != null ? 'SET' : 'NULL'}');
            }

            // Log full URL construction details for debugging
            final effectiveBaseUrl = dio.options.baseUrl;
            
            // Construct the full URL by combining base URL and path
            final requestPath = request.parameters['path'].toString();
            
            // CRITICAL FIX: Do not modify paths for shim service, as they need to include /shim-service/ prefix
            // Only normalize other paths to avoid double slashes
            String url = effectiveBaseUrl;
            if (request.apiType == ApiType.shimService) {
              // For shim service, preserve the full path including /shim-service/ prefix
              if (requestPath.startsWith('/')) {
                // Keep the slash since shim service expects full paths with leading slash
                _logIsolate('✅ SHIM SERVICE PATH - Preserving full path with leading slash: $requestPath');
                url += requestPath;
              } else {
                // Add slash if it's missing
                _logIsolate('⚠️ SHIM SERVICE PATH - Adding leading slash to path: /$requestPath');
                url += '/' + requestPath;
              }
            } else {
              // For other API types, normalize paths to avoid double slashes
              if (requestPath.startsWith('/')) {
                _logIsolate('ℹ️ Path starts with "/", normalizing for URL construction: ${requestPath.substring(1)}');
                url += requestPath.substring(1);
              } else {
                url += requestPath;
              }
            }
            final fullUrl = url;
            _logIsolate('🔍 REQUEST URL DETAILS - API: ${request.apiType.name}, Base URL: "$effectiveBaseUrl", Path: "$requestPath", Full URL: "$fullUrl"');

            // Add any custom headers
            if (request.headers != null) {
              headers.addAll(request.headers!);
            }

            // Execute the request based on method
            // CRITICAL FIX: Use the fully constructed URL instead of just the path
            // This ensures our path handling logic for shim-service is respected
            _logIsolate('🔄 REQUEST EXECUTION - Using full URL: "$fullUrl" instead of raw path');            
            _performDioRequest(
              dio: dio,
              method: request.method,
              path: fullUrl, // Use the properly constructed URL with preserved /shim-service/ prefix
              queryParams: queryParams,
              data: data,
              headers: headers,
            ).then((Response dioResponse) {
              // Restore original base URL if it was temporarily changed
              if (originalBaseUrl != null) {
                dio.options.baseUrl = originalBaseUrl;
              }
              // Send successful response back to main isolate
              final response = NetworkResponse(
                id: request.id,
                success: true,
                data: dioResponse.data,
                statusCode: dioResponse.statusCode,
                metadata: {
                  'headers': dioResponse.headers.map.map(
                    (key, value) => MapEntry(key, value.join(',')),
                  ),
                },
              );

              mainSendPort.send(response.toJson());
            }).catchError((error) async {
              // Handle DioException and possible token refresh
              final DioException dioError = error is DioException
                  ? error
                  : DioException(
                      requestOptions: RequestOptions(path: path),
                      error: error.toString(),
                      type: DioExceptionType.unknown,
                    );
              
              // Check if this is a 401 error from shim service that might be due to expired token
              if (request.apiType == ApiType.shimService && 
                  dioError.response?.statusCode == 401 &&
                  !request.parameters['path'].toString().contains('/auth/salesforce/obtain-token')) {
                
                // Try to extract error code from response
                String? errorCode;
                try {
                  if (dioError.response?.data is Map) {
                    final data = dioError.response!.data as Map<String, dynamic>;
                    errorCode = data['code'] as String?;
                  }
                } catch (e) {
                  _logIsolate('Error extracting error code from 401 response: $e');
                }
                
                _logIsolate('⚠️ ISOLATE - 401 Error detected for shim service request. Error code: $errorCode');
                _logIsolate('🔍 DEBUG - Error code type: ${errorCode.runtimeType}');
                _logIsolate('🔍 DEBUG - Error code length: ${errorCode?.length}');
                _logIsolate('🔍 DEBUG - Checking if errorCode == "AccessTokenExpired": ${errorCode == 'AccessTokenExpired'}');

                // If this is an accessTokenExpired error, we need to notify the main isolate
                if (errorCode == 'AccessTokenExpired') {
                  _logIsolate('🔄 ISOLATE - Access token expired. Notifying main isolate to refresh token');
                  
                  // Send special response to main isolate indicating token refresh is needed
                  final response = NetworkResponse(
                    id: request.id,
                    success: false,
                    error: 'Access token expired',
                    statusCode: 401,
                    metadata: {
                      'requiresTokenRefresh': true,
                      'originalRequest': request.toJson(),
                      'apiType': request.apiType.name,
                    },
                  );
                  
                  mainSendPort.send(response.toJson());
                  return;
                }
              }
              
              // Restore original base URL if it was temporarily changed
              if (originalBaseUrl != null) {
                dio.options.baseUrl = originalBaseUrl;
              }

              // For other errors, send normal error response
              final response = NetworkResponse(
                id: request.id,
                success: false,
                error: dioError.message ?? dioError.error.toString(),
                statusCode: dioError.response?.statusCode,
                metadata: dioError.response?.data is Map
                    ? dioError.response!.data as Map<String, dynamic>
                    : {
                        'error': dioError.response?.data?.toString() ?? 'Unknown error'
                      },
              );

              mainSendPort.send(response.toJson());
            });
          } catch (e) {
            _logIsolate('Error processing request: $e');
            // Send error back to main isolate with generated ID
            final response = NetworkResponse(
              id: const Uuid().v4(),
              success: false,
              error: 'Error processing request: $e',
            );

            mainSendPort.send(response.toJson());
          }
        } else {
          _logIsolate('Received non-string message: $message');
        }
      });
    });
  }

  /// Updates authorization tokens for network requests
  Future<void> updateAuthTokens({
    String? accessToken,
    String? authorizationToken,
  }) async {
    if (_sendPort == null) {
      throw Exception('Network isolate not initialized');
    }

    // Store tokens locally for potential use in future requests
    _accessToken = accessToken;
    _authorizationToken = authorizationToken;
    
    if (_enableLogging) {
      print('🔑 MAIN ISOLATE - Updating auth tokens');
      print('🔑 MAIN ISOLATE - Access Token: ${_maskToken(_accessToken)}');
      print('🔑 MAIN ISOLATE - Authorization Token: ${_maskToken(_authorizationToken)}');
    }

    // Format for token update message to isolate
    final Map<String, dynamic> message = {
      'type': 'update_tokens',
      'accessToken': _accessToken,
      'authorizationToken': _authorizationToken,
    };

    if (_enableLogging) {
      print('🔑 MAIN ISOLATE - Sending token update message to isolate');
    }

    _sendPort!.send(jsonEncode(message));

    if (_enableLogging) {
      print('🔑 MAIN ISOLATE - Token update message sent to isolate');
    }
  }

  /// Updates base URLs for API types
  Future<void> updateBaseUrls(Map<ApiType, String> baseUrls) async {
    if (_sendPort == null) {
      if (_enableLogging) {
        print(
            '⚠️ MAIN ISOLATE - Cannot update base URLs, isolate not initialized');
      }
      return;
    }

    if (_enableLogging) {
      print('🔑 MAIN ISOLATE - Updating base URLs: $baseUrls');
    }

    // Store base URLs locally for new requests
    _baseUrls.addAll(baseUrls);

    // Send base URL update message
    _sendPort!.send(jsonEncode({
      'type': 'BASE_URL_UPDATE',
      'baseUrls': baseUrls.map((key, value) => MapEntry(key.name, value)),
    }));
  }

  /// Helper method to perform Dio requests in the isolate
  static Future<Response> _performDioRequest({
    required Dio dio,
    required String method,
    required String path,
    Map<String, dynamic>? queryParams,
    dynamic data,
    Map<String, dynamic>? headers,
  }) async {
    // SUPER EXPLICIT DEBUG PRINTS FOR REQUEST
    debugPrint('🚨🚨🚨 ISOLATE REQUEST - ${method} ${path}');
    print('🚨🚨🚨 ISOLATE REQUEST DATA - ${data}');
    try {
      Response response;
      
      // Normalize method case for consistency
      final methodUpper = method.toUpperCase();
      
      switch (methodUpper) {
        case 'GET':
          response = await dio.get(
            path,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        case 'POST':
          response = await dio.post(
            path,
            data: data,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        case 'PUT':
          response = await dio.put(
            path,
            data: data,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        case 'PATCH':
          response = await dio.patch(
            path,
            data: data,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        case 'DELETE':
          response = await dio.delete(
            path,
            data: data,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        case 'HEAD':
          response = await dio.head(
            path,
            queryParameters: queryParams,
            options: Options(headers: headers),
          );
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }
      
      return response;
    } catch (e) {
      _logIsolate('Error in Dio request to $path: $e');
      rethrow;
    }
  }

  // Duplicate method removed - merged into the primary implementation above

  /// Handle responses from the isolate
  void _handleIsolateResponse(dynamic message) {
    // Handle SendPort messages during handshake - UPDATE _sendPort with the new communication port!
    if (message is SendPort) {
      if (_enableLogging) {
        print('🔗 MAIN ISOLATE - Received SendPort from isolate (handshake complete)');
        print('🔧 MAIN ISOLATE - CRITICAL: Updating _sendPort to use new communication port: $message');
        print('🔧 MAIN ISOLATE - Old _sendPort was: $_sendPort');
      }
      
      // THIS IS THE FIX: Update _sendPort to use the new communication port from background isolate
      _sendPort = message;
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Successfully updated _sendPort to new communication port: $_sendPort');
        print('🎉 MAIN ISOLATE - Isolate communication setup is now COMPLETE!');
      }
      return;
    }
    
    // Handle JSON string responses from actual requests
    if (message is String) {
      try {
        // Decode the message
        final Map<String, dynamic> decodedMessage = json_utils.safeJsonDecode(
            message);

        // Check if this is a response to a request
        final String? requestId = decodedMessage['id'];
        if (requestId == null) {
          if (_enableLogging) {
            print(
                '⚠️ MAIN ISOLATE - Received message without ID: $decodedMessage');
          }
          return;
        }

        // Find the pending request
        final completer = _pendingRequests.remove(requestId);
        if (completer == null) {
          if (_enableLogging) {
            print(
                '⚠️ MAIN ISOLATE - Received response for unknown request: $requestId');
          }
          return;
        }

        // Convert to NetworkResponse with error handling
        NetworkResponse response;
        try {
          response = NetworkResponse.fromMap(decodedMessage);
          if (_enableLogging) {
            print('✅ MAIN ISOLATE - Successfully parsed NetworkResponse for $requestId');
          }
        } catch (e) {
          if (_enableLogging) {
            print('❌ MAIN ISOLATE - Error parsing NetworkResponse for $requestId: $e');
            print('🔍 MAIN ISOLATE - Raw decoded message: $decodedMessage');
          }
          // Create error response if parsing fails
          response = NetworkResponse(
            id: requestId,
            success: false,
            error: 'Failed to parse response: $e',
            statusCode: 500,
          );
        }
        
        // Check if this is a token expiration error that requires refresh
        if (!response.success && 
            response.statusCode == 401 && 
            response.metadata != null && 
            response.metadata!['requiresTokenRefresh'] == true && 
            response.metadata!['originalRequest'] != null) {
          
          if (_enableLogging) {
            print('🔄 MAIN ISOLATE - Detected token expiration. Attempting token refresh and request retry');
          }
          
          // Handle token refresh and retry logic
          _handleTokenRefreshAndRetry(response).then((retryResponse) {
            // After successful retry, complete with the new response
            completer.complete(retryResponse);
          }).catchError((retryError) {
            // If retry fails, complete with error
            completer.completeError(retryError);
          });
          
          return; // Don't complete the completer yet, will be done after retry
        }

        // Complete the request with success or error
        if (response.success) {
          completer.complete(response.data);
        } else {
          final error = DioException(
            requestOptions: RequestOptions(path: 'unknown'),
            error: response.error ?? 'Unknown error',
            response: Response(
              statusCode: response.statusCode ?? 500,
              requestOptions: RequestOptions(path: 'unknown'),
              data: response.metadata,
            ),
            type: DioExceptionType.badResponse,
          );
          completer.completeError(error);
        }
      } catch (e) {
        if (_enableLogging) {
          print('⚠️ MAIN ISOLATE - Error processing isolate response: $e');
        }
      }
    } else if (_enableLogging) {
      print(
          '⚠️ MAIN ISOLATE - Received unexpected message type from isolate: ${message.runtimeType}');
    }
  }
  
  /// Handle token refresh and retry a failed request
  Future<dynamic> _handleTokenRefreshAndRetry(NetworkResponse errorResponse) async {
    if (_enableLogging) {
      print('🔄 MAIN ISOLATE - Starting token refresh and retry process');
      print('🔍 DEBUG - errorResponse type: ${errorResponse.runtimeType}');
      print('🔍 DEBUG - errorResponse.metadata type: ${errorResponse.metadata?.runtimeType}');
      print('🔍 DEBUG - errorResponse.metadata keys: ${errorResponse.metadata?.keys.toList()}');
      if (errorResponse.metadata != null && errorResponse.metadata!.containsKey('originalRequest')) {
        print('🔍 DEBUG - originalRequest type: ${errorResponse.metadata!['originalRequest'].runtimeType}');
        print('🔍 DEBUG - originalRequest value: ${errorResponse.metadata!['originalRequest']}');
      }
    }

    try {
      // Get the AuthUseCase from GetIt
      final authUseCase = GetIt.instance<AuthUseCase>();
      
      // Refresh the shim service token
      if (_enableLogging) {
        print('🔑 MAIN ISOLATE - Calling authUseCase.loginShimWithSalesForce() to refresh token');
      }
      
      final loginSuccess = await authUseCase.loginShimWithSalesForce();
      
      if (!loginSuccess) {
        if (_enableLogging) {
          print('❌ MAIN ISOLATE - Token refresh failed');
        }
        throw DioException(
          requestOptions: RequestOptions(path: 'unknown'),
          error: 'Failed to refresh token',
          type: DioExceptionType.unknown,
        );
      }
      
      // Get the original request details from metadata
      if (_enableLogging) {
        print('🔍 DEBUG - About to parse originalRequest');
        print('🔍 DEBUG - originalRequest actual type: ${errorResponse.metadata!['originalRequest'].runtimeType}');
        print('🔍 DEBUG - originalRequest actual value: ${errorResponse.metadata!['originalRequest']}');
      }

      // Parse the originalRequest JSON string to Map
      final originalRequestData = errorResponse.metadata!['originalRequest'];
      final Map<String, dynamic> originalRequestJson;

      if (originalRequestData is String) {
        // Parse JSON string to Map
        originalRequestJson = jsonDecode(originalRequestData) as Map<String, dynamic>;
      } else if (originalRequestData is Map<String, dynamic>) {
        // Already a Map, use directly
        originalRequestJson = originalRequestData;
      } else {
        throw Exception('originalRequest is neither String nor Map: ${originalRequestData.runtimeType}');
      }
      final String method = originalRequestJson['method'] as String;
      final ApiType apiType = ApiType.values.firstWhere(
        (e) => e.name == (originalRequestJson['apiType'] as String),
        orElse: () => ApiType.shimService,
      );
      if (_enableLogging) {
        print('🔍 DEBUG - originalRequestJson keys: ${originalRequestJson.keys.toList()}');
        print('🔍 DEBUG - parameters type: ${originalRequestJson['parameters'].runtimeType}');
        print('🔍 DEBUG - parameters value: ${originalRequestJson['parameters']}');
      }

      // Handle parameters safely - it might be a JSON string that needs parsing
      Map<String, dynamic> parameters;
      try {
        final parametersRaw = originalRequestJson['parameters'];
        if (parametersRaw is Map<String, dynamic>) {
          parameters = parametersRaw;
        } else if (parametersRaw is Map) {
          parameters = Map<String, dynamic>.from(parametersRaw);
        } else if (parametersRaw is String) {
          // Parameters is stored as a JSON string, parse it
          if (_enableLogging) {
            print('🔄 MAIN ISOLATE - Parameters is a JSON string, parsing...');
          }
          final parsedJson = json.decode(parametersRaw);
          if (parsedJson is Map<String, dynamic>) {
            parameters = parsedJson;
          } else if (parsedJson is Map) {
            parameters = Map<String, dynamic>.from(parsedJson);
          } else {
            throw Exception('Parsed parameters is not a Map: ${parsedJson.runtimeType}');
          }
        } else {
          throw Exception('Parameters is not a Map or String: ${parametersRaw.runtimeType}');
        }
      } catch (e) {
        if (_enableLogging) {
          print('❌ MAIN ISOLATE - Failed to extract parameters: $e');
        }
        rethrow;
      }

      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Token refresh successful. Retrying original request: $method ${parameters['path']}');
      }
      
      // Extract path and other parameters from the original request
      String path = parameters['path'] as String? ?? '';
      final String? baseUrl = parameters['baseUrl'] as String?;

      // Handle queryParams safely - it might be null or a different type
      Map<String, dynamic>? queryParams;
      try {
        final queryParamsRaw = parameters['queryParams'];
        if (queryParamsRaw != null) {
          if (queryParamsRaw is Map) {
            queryParams = Map<String, dynamic>.from(queryParamsRaw);
          } else if (queryParamsRaw is String) {
            // If it's a string, it might be JSON encoded
            if (_enableLogging) {
              print('🔄 MAIN ISOLATE - queryParams is String, skipping for retry');
            }
            queryParams = null;
          }
        }
      } catch (e) {
        if (_enableLogging) {
          print('⚠️ MAIN ISOLATE - Could not parse queryParams: $e');
        }
        queryParams = null;
      }

      // Handle request data safely
      dynamic requestData;
      try {
        requestData = parameters['data'];
        if (_enableLogging) {
          print('🔄 MAIN ISOLATE - Request data type: ${requestData.runtimeType}');
        }
      } catch (e) {
        if (_enableLogging) {
          print('⚠️ MAIN ISOLATE - Could not extract request data: $e');
        }
        requestData = null;
      }

      // For keep-alive and end session requests, update the session token in the URL path
      final keepAliveRegExp = RegExp(r'^/shim-service/organizations/([^/]+)/sessions/([^/]+)/actions/keep-alive$');
      final endSessionRegExp = RegExp(r'^/shim-service/organizations/([^/]+)/sessions/([^/]+)$');

      if (keepAliveRegExp.hasMatch(path) || endSessionRegExp.hasMatch(path)) {
        if (_enableLogging) {
          print('🔄 MAIN ISOLATE - Detected session-based endpoint. Updating session token in URL path');
        }

        // For session-based endpoints, we need to update the session token in the URL
        // Use the fresh session token that was just updated
        if (_accessToken != null && _accessToken!.isNotEmpty) {
          if (keepAliveRegExp.hasMatch(path)) {
            // Update keep-alive URL with fresh session token
            path = path.replaceFirstMapped(keepAliveRegExp, (match) {
              final orgId = match.group(1)!;
              return '/shim-service/organizations/$orgId/sessions/$_accessToken/actions/keep-alive';
            });
            if (_enableLogging) {
              print('🔄 MAIN ISOLATE - Updated keep-alive URL with fresh session token: $path');
            }
          } else if (endSessionRegExp.hasMatch(path)) {
            // Update end session URL with fresh session token
            path = path.replaceFirstMapped(endSessionRegExp, (match) {
              final orgId = match.group(1)!;
              return '/shim-service/organizations/$orgId/sessions/$_accessToken';
            });
            if (_enableLogging) {
              print('🔄 MAIN ISOLATE - Updated end session URL with fresh session token: $path');
            }
          }
        } else {
          if (_enableLogging) {
            print('⚠️ MAIN ISOLATE - No fresh session token available for URL update');
          }
        }
      }

      // Create new NetworkIsolateRequest for retry with fresh tokens
      if (_enableLogging) {
        print('🔄 MAIN ISOLATE - Creating retry request with:');
        print('   - apiType: $apiType');
        print('   - method: $method');
        print('   - path: $path');
        print('   - baseUrl: $baseUrl');
        print('   - queryParams type: ${queryParams?.runtimeType}');
        print('   - data type: ${requestData?.runtimeType}');
      }

      final retryRequest = NetworkIsolateRequest(
        apiType: apiType,
        method: method,
        path: path,
        baseUrl: baseUrl,
        queryParams: queryParams,
        data: requestData,
        // We'll use fresh tokens from credentials that were just refreshed by loginShimWithSalesForce
        accessToken: null,  // Will be fetched from credentials by sendRequest
        authorizationToken: null, // Will be fetched from credentials by sendRequest
      );
      
      // Retry the original request with fresh tokens
      final retryResponse = await sendRequest(retryRequest);
      
      if (_enableLogging) {
        print('✅ MAIN ISOLATE - Retry request succeeded');
      }
      
      return retryResponse;
    } catch (e) {
      if (_enableLogging) {
        print('❌ MAIN ISOLATE - Error during token refresh and retry: $e');
      }
      rethrow;
    }
  }
}
