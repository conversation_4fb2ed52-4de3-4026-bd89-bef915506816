import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/api_error.dart';
import 'package:x1440/api/dtos/salesforce_token_body.dart';
import 'package:x1440/api/dtos/refresh_salesforce_token_response.dart';
import 'package:x1440/api/dtos/salesforce_token_response.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/api/salesforce/dtos/oauth_response.dart';
import 'package:x1440/api/salesforce/dtos/revoke_token_body.dart';
import 'package:x1440/frameworks/notifications/notifications_event.dart';
import 'package:x1440/frameworks/notifications/notifications_manager.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';

import 'package:x1440/services/interfaces/contact_viewmodel_interface.dart';
import 'package:x1440/ui/blocs/org/org_bloc.dart';
import 'package:x1440/ui/blocs/org/org_event.dart';
import 'package:x1440/use_cases/contacts/contacts_use_case.dart';
import 'package:x1440/use_cases/conversations/conversations_use_case.dart';
import 'package:x1440/use_cases/settings/settings_use_case.dart';
import 'package:x1440/repositories/auth/auth_repository.dart';
import 'package:x1440/repositories/life_cycle/app_life_cycle_repository.dart';
import 'package:x1440/repositories/models/active_presence_status.dart';
import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/services/de_conversations_service.dart';
import 'package:x1440/ui/blocs/auth/auth_bloc.dart';
import 'package:x1440/ui/blocs/auth/auth_event.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/previously_logged_in_user.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';
import 'package:x1440/use_cases/session/session_use_case.dart';
import 'package:x1440/utils/Utils.dart';
import 'package:x1440/viewmodels/conversations_viewmodel.dart';

class AuthUseCase {
  final AuthRepository _authRepository;
  final LocalStorageRepository _localStorage;
  final AppConfig _appConfig;
  final NotificationManager _notificationManager;
  final RemoteLogger _remoteLogger;

  AuthUseCase(this._authRepository, this._localStorage, this._appConfig,
      this._notificationManager, this._remoteLogger) {
    // LIFECYCLE LOGGING: Listen to app lifecycle events to track token persistence
    _initializeLifecycleLogging();
  }

  void _initializeLifecycleLogging() {
    try {
      final appLifeCycleRepository = GetIt.I<AppLifeCycleRepository>();
      appLifeCycleRepository.appLifeCycleStream.listen((state) {
        _onAppLifecycleStateChanged(state);
      });
      _remoteLogger.info("🔄 LIFECYCLE - App lifecycle logging initialized");
    } catch (e) {
      _remoteLogger.error("🔄 LIFECYCLE - Failed to initialize lifecycle logging: $e");
    }
  }

  void _onAppLifecycleStateChanged(AppLifecycleState state) async {
    try {
      // Special handling for app termination/backgrounding
      if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
        await _ensureTokensPersisted();
      }

      // Special handling for app resumption
      if (state == AppLifecycleState.resumed) {
        await _verifyTokenIntegrityAfterResume();
      }

    } catch (e) {
      _remoteLogger.error("Error during lifecycle state change to $state: $e");
    }
  }

  Future<void> _ensureTokensPersisted() async {
    try {
      final credentials = await _localStorage.getCredentials();

      if (credentials.refreshToken != null) {
        // Force save to secure storage
        await Utils.saveDataToSecureStorage('refresh_token', credentials.refreshToken);
        await Utils.saveDataToSecureStorage('access_token', credentials.accessToken);
        await Utils.saveDataToSecureStorage('org_id', credentials.orgId);
        await Utils.saveDataToSecureStorage('user_id', credentials.userId);
        await Utils.saveDataToSecureStorage('instance_url', credentials.instanceUrl);

        // Verify persistence
        final verifyRefreshToken = await Utils.getDataFromSecureStorage('refresh_token');
        final tokensMatch = credentials.refreshToken == verifyRefreshToken;

        if (!tokensMatch) {
          _remoteLogger.error("Token persistence verification failed!");
        }
      }
    } catch (e) {
      _remoteLogger.error("Error ensuring token persistence: $e");
    }
  }

  Future<void> _verifyTokenIntegrityAfterResume() async {
    try {
      final credentials = await _localStorage.getCredentials();
      final secureRefreshToken = await Utils.getDataFromSecureStorage('refresh_token');

      final tokensMatch = credentials.refreshToken == secureRefreshToken;

      if (!tokensMatch) {
        _remoteLogger.warn("Token integrity compromised after resume - this may explain auto-login failures");

        // Try to recover from secure storage
        if (secureRefreshToken != null && credentials.refreshToken == null) {
          // This would require updating the credentials, but we should be careful about this
        }
      }
    } catch (e) {
      _remoteLogger.error("Error verifying token integrity: $e");
    }
  }

  Future<void> setCredentials(Credentials credentials) async {
    // Inspect the orgId and truncate to 15 because of SalesForce
    if (credentials.orgId != null && credentials.orgId!.length > 15) {
      credentials =
          credentials.copyWith(orgId: credentials.orgId!.substring(0, 15));
    }
    await _localStorage.setCredentials(credentials);
  }

  Stream<Credentials> get credentialsStream => _localStorage.credentialsStream;

  Future<Credentials> getCredentials() async {
    return await _localStorage.getCredentials();
  }

  Future<bool> get isLoggedIn =>
      _localStorage.getCredentials().then((value) => value.isLoggedIn);

  Future<bool> get sfLoggedIn =>
      _localStorage.getCredentials().then((value) => value.sfLoggedIn);

  Completer<bool>? _loginShimCompleter;

  Future<bool> loginShimWithSalesForce() async {
    if (_loginShimCompleter != null) {
      return _loginShimCompleter!.future;
    }

    _loginShimCompleter = Completer<bool>();

    try {
      final credentials = await _localStorage.getCredentials();

      if (credentials.accessToken == null ||
          credentials.orgId == null ||
          _isLoggingOut) {
        _loginShimCompleter!.complete(false);
        return false;
      }

      final response = await _authRepository.authShimServiceFromSalesforce(
        SalesforceTokenBody(
          orgId: credentials.orgId!,
          accessToken: credentials.accessToken!,
          expirationSeconds:
              _appConfig.globalConfig?.shimServiceExpirationInSeconds,
        ),
      );

      if (response is Success) {
        final result = response as Success;
        final payload = result.data as SalesforceTokenResponse;

        await _localStorage.setCredentials(credentials.copyWith(
          orgId: credentials.orgId!,
          accessToken: credentials.accessToken!,
          authorizationToken: payload.accessToken,
        ));

        // Update authorization token in network isolate for subsequent shim service requests
        try {
          final networkServiceFactory = GetIt.instance<NetworkServiceFactory>();

          await networkServiceFactory.updateAuthTokens(
            authorizationToken: payload.accessToken,
            accessToken: credentials.sessionToken!, // Use session token for X-1440-Session-Token header
          );
        } catch (networkError) {
          _remoteLogger.error('ERROR updating authorization token in network isolate: $networkError');
          // Continue execution even if network isolate update fails
        }

        _loginShimCompleter!.complete(true);
        return true;
      } else {
        final error = (response as Error).error;
        _remoteLogger.warn('Failed to login shim: $error');
        _loginShimCompleter!.complete(false);
        return false;
      }
    } catch (e) {
      _remoteLogger.error('Error during login shim: $e');
      _loginShimCompleter!.completeError(e);
      return false;
    } finally {
      _loginShimCompleter = null;
    }
  }

  Future<void> shimLoginAndStartSession() async {
    if (_isLoggingOut) {
      return;
    }

    try {
      await loginShimWithSalesForce();
    } catch (e) {
      _remoteLogger.error("ERROR in loginShimWithSalesForce: $e");
      throw e; // Re-throw to maintain error handling
    }

    final credentials = await _localStorage.getCredentials();

    // Save auth token in secure storage as well as instanceUrl
    try {
      await Utils.saveDataToSecureStorage(
          'access_token', credentials.accessToken);
      await Utils.saveDataToSecureStorage(
          'shim_service_url', _appConfig.shimServiceUrl);
      await Utils.saveDataToSecureStorage(
          'instance_url',
          credentials
              .instanceUrl); // e.g. https://re1693247397909.my.salesforce.com
    } catch (e) {
      _remoteLogger.error("ERROR saving to secure storage: $e");
      // Continue execution even if secure storage fails
    }

    try {
      await GetIt.I<SessionUseCase>().startSession();
    } catch (e) {
      _remoteLogger.error("ERROR in SessionUseCase.startSession: $e");
      throw e; // Re-throw to maintain error handling
    }

    _isLoggingOut = false;
  }

  Future<bool> attemptAutoLogin() async {
    final isSfLoggedIn = await sfLoggedIn;

    if (isSfLoggedIn) {
      try {
        final refreshResult = await refreshSalesforceToken();

        if (refreshResult) {
          try {
            await shimLoginAndStartSession();
          } catch (shimError) {
            _remoteLogger.error("ERROR in shimLoginAndStartSession: $shimError");
            return false;
          }

          final loggedIn = await isLoggedIn;

          if (!loggedIn) {
            _remoteLogger.warn("attemptAutoLogin loggedIn is false after successful shimLoginAndStartSession");
          }

          return loggedIn;
        }

        _remoteLogger.warn("attemptAutoLogin failed to refreshSalesforceToken");
      } catch (e) {
        _remoteLogger.warn("attemptAutoLogin failed with error: $e");
        return false;
      }
    }

    return false;
  }

  Completer<bool>? _refreshTokenCompleter;

  Future<bool> refreshSalesforceToken() async {
    if (_refreshTokenCompleter != null) {
      return _refreshTokenCompleter!.future;
    }
    _refreshTokenCompleter = Completer<bool>();

    try {
      final credentials = await _localStorage.getCredentials();

      // Ensure authorization token is available in network isolate for refresh request
      if (credentials.authorizationToken != null) {
        try {
          await GetIt.I<NetworkServiceFactory>().updateAuthTokens(
            accessToken: credentials.sessionToken,
            authorizationToken: credentials.authorizationToken,
          );
        } catch (updateError) {
          _remoteLogger.error("Failed to update network isolate: $updateError");
        }
      } else {
        _remoteLogger.warn("No authorization token available for network isolate update");
      }

      // Check secure storage for token comparison
      try {
        final secureRefreshToken = await Utils.getDataFromSecureStorage('refresh_token');
        final tokensMatch = credentials.refreshToken == secureRefreshToken;
        if (!tokensMatch) {
          _remoteLogger.warn("TOKEN MISMATCH DETECTED between localStorage and secureStorage!");
        }
      } catch (secureStorageError) {
        _remoteLogger.error("Error reading secure storage: $secureStorageError");
      }

      if (credentials.refreshToken == null) {
        _remoteLogger.warn("NO REFRESH TOKEN AVAILABLE - triggering logout");
        _refreshTokenCompleter!.complete(false);
        GetIt.I<AuthBloc>().add(LogoutEvent());
        return false;
      }

      // Use obtain-token approach instead of broken refresh-token endpoint
      final response = await _authRepository.authShimServiceFromSalesforce(
        SalesforceTokenBody(
          orgId: credentials.orgId!,
          accessToken: credentials.accessToken!,
          expirationSeconds: _appConfig.globalConfig?.shimServiceExpirationInSeconds,
        ),
      );

      if (response is Success) {
        final result = response as Success;
        final payload = result.data as SalesforceTokenResponse;

        final updatedCredentials = credentials.copyWith(
            authorizationToken: payload.accessToken);
        await _localStorage.setCredentials(updatedCredentials);

        // Verify credentials were saved correctly
        try {
          final verifyCredentials = await _localStorage.getCredentials();
          final tokensMatch = verifyCredentials.authorizationToken == payload.accessToken;
          if (!tokensMatch) {
            _remoteLogger.error("VERIFICATION FAILED - authorization tokens don't match!");
          }
        } catch (verifyError) {
          _remoteLogger.error("VERIFICATION ERROR: $verifyError");
        }

        // Update network isolate with new authorization token
        try {
          await GetIt.I<NetworkServiceFactory>().updateAuthTokens(
            authorizationToken: payload.accessToken,
            accessToken: credentials.sessionToken,
          );
        } catch (updateError) {
          _remoteLogger.error("Failed to update network isolate: $updateError");
        }

        _refreshTokenCompleter!.complete(true);
        return true;
      } else {
        final errorResponse = (response as Error).error;
        _remoteLogger.warn('Failed to refresh token: $errorResponse');

        _refreshTokenCompleter!.complete(false);

        _remoteLogger.warn('Token refresh failed, triggering logout');
        GetIt.I<AuthBloc>().add(LogoutEvent());

        return false;
      }
    } catch (e) {
      _remoteLogger.error('Error refreshing token: $e');
      if (!_refreshTokenCompleter!.isCompleted) {
        _refreshTokenCompleter!.completeError(e);
      }

      _remoteLogger.warn('Exception in token refresh, triggering logout');
      GetIt.I<AuthBloc>().add(LogoutEvent());

      return false;
    } finally {
      _refreshTokenCompleter = null;
    }
  }

  Future<SalesforceConfig> _getSfConfigFromSelectedEnvironment() async {
    final appLocalSettings =
        await GetIt.instance<SettingsUseCase>().appLocalSettings;
    final env = appLocalSettings.selectedEnvironment;

    switch (env.type) {
      case SalesforceEnvironmentType.production:
        return _appConfig.productionSalesforceConfig!;
      case SalesforceEnvironmentType.sandbox:
        return _appConfig.sandboxSalesforceConfig!;
      case SalesforceEnvironmentType.custom:
        return _appConfig.productionSalesforceConfig!.copyWith(
            endPointBase: appLocalSettings.selectedEnvironment.domain);
      default:
        return _appConfig.sandboxSalesforceConfig!;
    }
  }

  Future<Result<OAuthResponse, ApiError>> loginToSalesforce() async {
    final sfConfig = await getSalesforceConfig();
    return await _authRepository.loginToSalesforce(
        sfConfig, Utils.getLanguage());
  }

  Future<void> handleOAuthResponse(OAuthResponse payload) async {
    await _localStorage.setScrtCredentials(const ScrtCredentials());
    final creds = await getCredentials();

    final newCredentials = creds.copyWith(
        userId: payload.userId,
        instanceUrl: payload.instanceUrl,
        accessToken: payload.accessToken,
        orgId: payload.orgId,
        refreshToken: payload.refreshToken,
        webSocketUrl: null);
    await setCredentials(newCredentials);

    // Save refresh token to secure storage immediately
    try {
      await Utils.saveDataToSecureStorage('refresh_token', payload.refreshToken);
      await Utils.saveDataToSecureStorage('access_token', payload.accessToken);
      await Utils.saveDataToSecureStorage('org_id', payload.orgId);
      await Utils.saveDataToSecureStorage('user_id', payload.userId);
      await Utils.saveDataToSecureStorage('instance_url', payload.instanceUrl);

      // Verify immediate persistence
      final verifyRefreshToken = await Utils.getDataFromSecureStorage('refresh_token');
      final tokensMatch = payload.refreshToken == verifyRefreshToken;
      if (!tokensMatch) {
        _remoteLogger.error("IMMEDIATE VERIFICATION FAILED - tokens don't match!");
      }
    } catch (e) {
      _remoteLogger.error("ERROR saving initial tokens to secure storage: $e");
    }

    PreviouslyLoggedInUser? previouslyLoggedInUser =
        await _localStorage.getPreviouslyLoggedInUser();
    if (previouslyLoggedInUser == null) {
      await _localStorage.setPreviouslyLoggedInUser(
          PreviouslyLoggedInUser(userId: payload.userId, orgId: payload.orgId));
      await _localStorage.clearActivePresenceStatus();
    } else {
      if (previouslyLoggedInUser.userId != payload.userId ||
          previouslyLoggedInUser.orgId != payload.orgId) {
        await _localStorage.clearPreviouslyLoggedInUser();
        await _localStorage.setPreviouslyLoggedInUser(PreviouslyLoggedInUser(
            userId: payload.userId,
            orgId: payload.orgId,
            presenceId: ActivePresenceStatus.offline().id));
        await _localStorage.clearActivePresenceStatus();
      }
    }
  }

  bool get isLoggingOut => _isLoggingOut;
  bool _isLoggingOut = false;
  void setIsLoggingOut([bool? value]) {
    _isLoggingOut = value ?? true;
  }

  Future<bool> logout() async {
    _remoteLogger.info("logout called");
    _isLoggingOut = true;
    _notificationManager.add(ClearNotificationsEvent());
    
    // First explicitly disconnect the websocket to prevent reconnection attempts
    try {
      _remoteLogger.info("Explicitly disconnecting websocket before logout");
      await GetIt.I.get<ShimWebsocketRepository>().disconnect();
    } catch (e) {
      _remoteLogger.error("Error disconnecting websocket during logout: $e");
    }
    
    await _localStorage.clearActivePresenceStatus();
    Credentials credentials = await _localStorage.getCredentials();
    SalesforceConfig sfConfig = await _getSfConfigFromSelectedEnvironment();

    Result<bool, ApiError>? response;
    // TODO: handle failure
    if (credentials.refreshToken != null) {
      response = await _authRepository.logoutFromSalesForce(
          RevokeTokenBody(token: credentials.refreshToken!), sfConfig);
    }

    // Keep in sync the sharedPrefs
    await _localStorage.clearCredentials();
    await Utils.clearDataFromSecureStorage();
    await _localStorage.clearScrtCredentials();

    _isLoggingOut = false;

    // Delete all cookies on inappwebview
    await CookieManager().deleteAllCookies();

    // TODO: improve this clearing -- GetIt Scopes; AuthService?
    GetIt.I.get<DeConversationsService>().resetScrtCredentials();
    GetIt.I.get<ConversationsViewmodel>().clear();
    GetIt.I.get<ConversationsUseCase>().clear();
    GetIt.I.get<ContactsUseCase>().clear();
    GetIt.I.get<ContactsViewmodelInterface>().clear();
    GetIt.I.get<ConversationsBloc>().add(ClearConversationsEvent());
    GetIt.I.get<OrgBloc>().add(ClearOrgEvent());

    return response is Success;
  }

  Future<SalesforceConfig> getSalesforceConfig() async {
    return await _getSfConfigFromSelectedEnvironment();
  }
}
