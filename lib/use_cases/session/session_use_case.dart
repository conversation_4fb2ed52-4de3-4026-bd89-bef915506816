import 'dart:async';

import 'package:get_it/get_it.dart';
import 'package:x1440/api/base_error.dart';
import 'package:x1440/api/dtos/keep_session_alive_response.dart';
import 'package:x1440/api/dtos/log_metadata.dart';

import 'package:x1440/api/dtos/sessions_body.dart';
import 'package:x1440/api/dtos/sessions_response.dart';
import 'package:x1440/api/isolate/network_service_factory.dart';
import 'package:x1440/frameworks/presence/presence_event.dart';
import 'package:x1440/frameworks/presence/presence_manager.dart';
import 'package:x1440/frameworks/push_notification_manager.dart';
import 'package:x1440/frameworks/remote_config/app_config.dart';
import 'package:x1440/repositories/environment_info/environment_info_repository.dart';
import 'package:x1440/repositories/session/session_repository.dart';

import 'package:x1440/repositories/storage/local_storage_repository.dart';
import 'package:x1440/repositories/websocket/shim_websocket_repository.dart';
import 'package:x1440/ui/blocs/conversations/conversations_bloc.dart';
import 'package:x1440/ui/blocs/conversations/conversations_event.dart';
import 'package:x1440/use_cases/logging/logging_use_case.dart';
import 'package:x1440/use_cases/messaging/messaging_use_case.dart';
import 'package:x1440/use_cases/models/credentials.dart';
import 'package:x1440/use_cases/models/scrt_credentials.dart';
import 'package:x1440/utils/Utils.dart';

class SessionUseCase {
  final SessionRepository _sessionRepository;
  final LocalStorageRepository _localStorage;
  final EnvironmentInfoRepository _environmentInfoRepository;
  final AppConfig _appConfig;
  final RemoteLogger _logger;

  /// Mask a token for safe logging
  String _maskToken(String? token) {
    if (token == null || token.isEmpty) return 'NULL';
    if (token.length <= 8) return '****';
    return '${token.substring(0, 4)}...${token.substring(token.length - 4)}';
  }

  SessionUseCase(this._sessionRepository, this._localStorage,
      this._environmentInfoRepository, this._appConfig, this._logger);

  Completer? _endSessionCompleter;
  Future<void> endSession([String? sessionToken]) async {
    if (_endSessionCompleter != null) {
      if (_endSessionCompleter!.isCompleted) {
        _endSessionCompleter = null;
        return endSession(sessionToken);
      }
      await _endSessionCompleter!.future;
    }
    _endSessionCompleter = Completer();
    
    // First disconnect the websocket to prevent reconnection attempts
    try {
      _logger.info("Disconnecting websocket connection before ending session");
      await GetIt.I.get<ShimWebsocketRepository>().disconnect();
    } catch (e) {
      _logger.error("Error disconnecting websocket during endSession: $e");
    }

    // TODO: refactor (this was in CVM)
    try {
      GetIt.I<MessagingUseCase>().clearOutboundMessagingSessions();
    } catch (e) {
      _logger.error('Error clearing out messaging sessions: $e');
    }

    final credentials = await _localStorage.getCredentials();

    sessionToken ??= credentials.sessionToken;
    if (credentials.orgId != null && sessionToken != null) {
      await _sessionRepository.endSession(credentials.orgId!, sessionToken);
      await _localStorage.setScrtCredentials(const ScrtCredentials());
      Credentials updatedCredentials = credentials.copyWith(
          sessionId: null, sessionToken: null, webSocketUrl: null);
      await _localStorage.setCredentials(updatedCredentials);
    }
    _endSessionCompleter?.complete();
    _endSessionCompleter = null;
  }

  Completer<bool>? _startSessionCompleter;
  Future<bool> startSession() async {
    final creds = await _localStorage.getCredentials();
    final userId = creds.userId;
    final instanceUrl = creds.instanceUrl;

    if (userId == null || instanceUrl == null) {
      return false;
    }

    if (_startSessionCompleter != null) {
      return _startSessionCompleter!.future;
    }
    _startSessionCompleter = Completer();

    try {
      /// clear out any existing scrt credentials
      await _localStorage.setScrtCredentials(const ScrtCredentials());

      String? token;
      final pushManager = GetIt.instance<PushNotificationManager>();

      final accepted = await pushManager.requestPermission();
      if (accepted) {
        token = await pushManager.getPushToken();
      }

      if (token == null) {
        _logger.warn('session start failed - push token is null');
        // return false;
      }

      final credentials = await _localStorage.getCredentials();

      String? accessToken = credentials.accessToken;
      String? orgId = credentials.orgId;

      if (orgId != null && accessToken != null) {
        final LogMetadata metadata =
            await _environmentInfoRepository.getLogMetadata();

        // Log the full session request details for debugging
        final sessionBody = SessionsBody(
                expirationSeconds:
                    _appConfig.globalConfig?.sessionExpirationInSeconds,
                accessToken: accessToken, // Use shim service token instead of Salesforce token
                userId: userId,
                locale: Utils.getLocale(),
                instanceUrl: instanceUrl,
                deviceToken: token,
                channelPlatformTypes: ['omni', 'x1440'],
                metadata: metadata.toJson());
        
        _logger.info('SESSION START REQUEST - OrgId: $orgId');
        _logger.info('SESSION START REQUEST - UserId: $userId');
        _logger.info('SESSION START REQUEST - InstanceUrl: $instanceUrl');
        _logger.info('SESSION START REQUEST - AccessToken: ${_maskToken(accessToken)}');
        _logger.info('SESSION START REQUEST - DeviceToken: ${token != null ? 'SET' : 'NULL'}');
        
        final response = await _sessionRepository.startSession(
            orgId,
            sessionBody);
        if (response is Error) {
          _logger.warn('session start failed - ${(response as Error).error}');
          return false;
        } else {
          Success result = response as Success;
          SessionsResponse payload = result.data;
          Credentials updatedCredentials = credentials.copyWith(
              userId: userId,
              sessionId: payload.sessionId,
              sessionToken: payload.sessionToken,
              instanceUrl: instanceUrl,
              webSocketUrl: payload.webSocketUrl);

          /// NOTE: we only have both of these if we're using regular Omni; for enhanced, we don't get the scrtAccessToken until we've set presence status
          await _localStorage.setScrtCredentials(ScrtCredentials(
            scrtAccessToken: payload.scrtAccessToken,
            scrtHost: payload.scrtHost,
          ));
          await _localStorage.setCredentials(updatedCredentials);

          // Update network isolate with session token now that it's available
          try {
            final networkServiceFactory = GetIt.instance<NetworkServiceFactory>();
            await networkServiceFactory.updateAuthTokens(
              authorizationToken: updatedCredentials.authorizationToken,
              accessToken: payload.sessionToken, // Now we have the session token
            );
            _logger.info('✅ SESSION - Updated network isolate with session token');
          } catch (networkError) {
            _logger.error('ERROR updating network isolate with session token: $networkError');
            // Continue execution even if network isolate update fails
          }

          if (payload.scrtAccessToken != null && payload.scrtHost != null) {
            GetIt.I<ConversationsBloc>().add(FetchConversationsEvent());
          }

          if (payload.presenceStatuses != null) {
            GetIt.I<PresenceManager>().add(SetAvailablePresenceStatusesEvent(
                payload.presenceStatuses!, payload.presenceStatusId));
          }
        }
      } else {
        _logger.error('Session start failed - missing required tokens: orgId=${orgId != null}, shimAuthToken=${accessToken != null}');
      }
    } finally {
      _startSessionCompleter!.complete(true);
      _startSessionCompleter = null;
    }
    return true;
  }

  Future<bool> keepSessionAlive() async {
    final Credentials credentials = await _localStorage.getCredentials();

    if (credentials.orgId != null && credentials.sessionToken != null) {
      final response = await _sessionRepository.keepSessionAlive(
          credentials.orgId!, credentials.sessionToken!);
      if (response is Error) {
        return false;
        // throw ApiException((response as Error).error);
      }

      KeepSessionAliveResponse payload = (response as Success).data;

      final Credentials updatedCreds = await _localStorage.getCredentials();

      await _localStorage.setCredentials(
          updatedCreds.copyWith(sessionExpirationTime: payload.expirationTime));
      return true;
    }
    return false;
  }
}
